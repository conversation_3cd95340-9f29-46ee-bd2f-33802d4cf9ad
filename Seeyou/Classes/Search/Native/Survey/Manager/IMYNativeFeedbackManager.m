//
//  IMYNativeFeedbackManager.m
//  zzimymain
//
//

#import "IMYNativeFeedbackManager.h"
#import "IMYNativeFeedbackCardView.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import "IMYSurveySDK.h"
#import "IMYPopPlanAPI.h"
#import "IMYSurveyAPI.h"

@interface IMYNativeFeedbackManager ()

@property (nonatomic, strong) IMYNativeFeedbackCardView *currentCardView;
@property (nonatomic, copy) IMYNativeFeedbackResultBlock currentCompletion;

// 数据请求相关
@property (nonatomic, assign) BOOL isRequestingData;
@property (nonatomic, strong) IMYSurveyModel *surveyModel;

@end

@implementation IMYNativeFeedbackManager

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static IMYNativeFeedbackManager *instance = nil;
    dispatch_once(&onceToken, ^{
        instance = [[super allocWithZone:NULL] init];
    });
    return instance;
}

+ (id)allocWithZone:(struct _NSZone *)zone {
    return [self sharedInstance];
}

/// 预加载反馈数据（页面加载时调用）
- (void)preloadFeedbackDataWithCompletion:(nullable IMYNativeFeedbackDataPreloadBlock)completion {
    // 预加载时不需要具体的feedbackWords和scheme，只获取基础数据
    @weakify(self);
    [self requestBaseSurveyDataWithCompletion:^(IMYSurveyModel *surveyModel) {
        @strongify(self);
        self.surveyModel = surveyModel;
        if (completion) {
            completion(surveyModel);
        }
    }];
}

/// 直接显示反馈卡片（使用预加载的数据）
- (void)showFeedbackCardDirectlyWithWords:(NSArray<NSString *> *)feedbackWords
                                   scheme:(IMYNativeFeedbackScheme)scheme
                               completion:(nullable IMYNativeFeedbackResultBlock)completion {
    if (!feedbackWords || feedbackWords.count == 0) {
        if (completion) {
            completion(NO, nil);
        }
        return;
    }

    if (!self.surveyModel) {
        if (completion) {
            completion(NO, nil);
        }
        return;
    }

    [self dismissCurrentCard];
    self.currentCompletion = completion;

    // 使用预加载的数据，处理feedbackWords和scheme
    IMYSurveyModel *processedModel = [self processSurveyModel:self.surveyModel
                                             withFeedbackWords:feedbackWords
                                                        scheme:scheme];
    if (processedModel) {
        [self showCardWithSurveyModel:processedModel];
    } else {
        if (completion) {
            completion(NO, nil);
        }
    }
}

/// 请求数据+展示
- (void)showFeedbackCardWithWords:(NSArray<NSString *> *)feedbackWords
                           scheme:(IMYNativeFeedbackScheme)scheme
                       completion:(nullable IMYNativeFeedbackResultBlock)completion {
    if (!feedbackWords || feedbackWords.count == 0) {
        if (completion) {
            completion(NO, nil);
        }
        return;
    }

    [self dismissCurrentCard];
    self.currentCompletion = completion;

    [self requestSurveyDataWithFeedbackWords:feedbackWords scheme:scheme completion:^(IMYSurveyModel *surveyModel) {
        if (surveyModel) {
            [self showCardWithSurveyModel:surveyModel];
        } else {
            completion(NO, nil);
        }
    }];
}

/// 请求基础问卷数据（不包含具体的feedbackWords处理）
- (void)requestBaseSurveyDataWithCompletion:(void (^)(IMYSurveyModel *surveyModel))completion {
    // 第一步：获取投放计划
    NSString *code = @"search-guess-neg";

    @weakify(self);
    [self getPlanWithCode:code onSuccess:^(NSDictionary * _Nonnull planDict) {
        @strongify(self);
        // 从投放计划中提取surveyId
        NSString *surveyId = [self extractSurveyIdFromPlanDict:planDict];

        if (imy_isEmptyString(surveyId)) {
            if (completion) {
                completion(nil);
            }
            return;
        }
        // 直接发起网络请求获取数据
        [self getSurveyDetailWithSurveyId:surveyId onSuccess:^(NSDictionary * _Nonnull surveyDict) {
            @strongify(self);

            // 将字典转换为IMYSurveyModel
            IMYSurveyModel *surveyModel = [surveyDict toModel:IMYSurveyModel.class];

            if (surveyModel && surveyModel.question_list.count > 0) {
                if (completion) {
                    completion(surveyModel);
                }
            } else {
                if (completion) {
                    completion(nil);
                }
            }
        } onError:^(NSError * _Nonnull error) {
            @strongify(self);
            if (completion) {
                completion(nil);
            }
        }];

    } onError:^(NSError * _Nonnull error) {
        @strongify(self);
        self.isRequestingData = NO;
        if (completion) {
            completion(nil);
        }
    }];
}

- (void)requestSurveyDataWithFeedbackWords:(NSArray<NSString *> *)feedbackWords
                                    scheme:(IMYNativeFeedbackScheme)scheme
                                completion:(void (^)(IMYSurveyModel *surveyModel))completion {
    if (self.isRequestingData) {
        if (completion) {
            completion(nil);
        }
        return;
    }

    self.isRequestingData = YES;

    // 第一步：获取投放计划
    NSString *code = @"search-guess-neg";

    @weakify(self);
    [self getPlanWithCode:code onSuccess:^(NSDictionary * _Nonnull planDict) {
        @strongify(self);

        // 从投放计划中提取surveyId
        NSString *surveyId = [self extractSurveyIdFromPlanDict:planDict];

        if (imy_isEmptyString(surveyId)) {
            self.isRequestingData = NO;
            if (completion) {
                completion(nil);
            }
            return;
        }

        // 直接发起网络请求获取数据
        [self getSurveyDetailWithSurveyId:surveyId onSuccess:^(NSDictionary * _Nonnull surveyDict) {
            @strongify(self);

            // 将字典转换为IMYSurveyModel
            IMYSurveyModel *surveyModel = [surveyDict toModel:IMYSurveyModel.class];

            if (surveyModel && surveyModel.question_list.count > 0) {
                IMYSurveyModel *processedModel = [self processSurveyModel:surveyModel withFeedbackWords:feedbackWords scheme:scheme];
                if (completion) {
                    completion(processedModel);
                }
            } else {
                if (completion) {
                    completion(nil);
                }
            }

            self.isRequestingData = NO;

        } onError:^(NSError * _Nonnull error) {
            @strongify(self);
            if (completion) {
                completion(nil);
            }
            self.isRequestingData = NO;
        }];

    } onError:^(NSError * _Nonnull error) {
        @strongify(self);
        self.isRequestingData = NO;
        if (completion) {
            completion(nil);
        }
    }];
}

/// 从投放计划字典中提取surveyId
- (NSString *)extractSurveyIdFromPlanDict:(NSDictionary *)planDict {
    // 参考IMYPopPlanModel的surveyId提取逻辑
    NSArray *list = planDict[@"list"];
    if (![list isKindOfClass:[NSArray class]] || list.count == 0) {
        return nil;
    }

    // 取第一个有效的计划
    for (NSDictionary *planItem in list) {
        if (![planItem isKindOfClass:[NSDictionary class]]) {
            continue;
        }

        // 按照IMYPopPlanModel.surveyId的逻辑：materials -> surveyMateriaList -> id
        NSArray *materials = planItem[@"materials"];
        if ([materials isKindOfClass:[NSArray class]] && materials.count > 0) {
            NSDictionary *material = materials.firstObject;
            if ([material isKindOfClass:[NSDictionary class]]) {
                // 查找surveyMateriaList
                NSArray *surveyMateriaList = material[@"list"];
                if ([surveyMateriaList isKindOfClass:[NSArray class]] && surveyMateriaList.count > 0) {
                    NSDictionary *surveyMateria = surveyMateriaList.firstObject;
                    if ([surveyMateria isKindOfClass:[NSDictionary class]]) {
                        NSString *surveyId = surveyMateria[@"id"];
                        if (imy_isNotEmptyString(surveyId)) {
                            return surveyId;
                        }
                    }
                }
            }
        }
    }

    return nil;
}

/// 获取投放计划
- (void)getPlanWithCode:(NSString *)code
              onSuccess:(void (^)(NSDictionary *dict))onSuccess
                onError:(void (^)(NSError *error))onError {
    NSMutableDictionary *parameter = [NSMutableDictionary dictionary];
    [parameter imy_setNonNilObject:code forKey:@"code"];

    RACSignal *signal = [IMYServerRequest postPath:@"dialog/popplan" host:mgo_seeyouyima_com params:parameter headers:nil];
    
    @weakify(self);
    RACSignal *resSignal = [signal flattenMap:^__kindof RACStream * _Nullable(id  _Nullable value) {
        @strongify(self);
        return [self.class responseFlattenMap:value];
    }];
    
    [[resSignal deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
        if (onSuccess) {
            onSuccess(x);
        }
    } error:^(NSError * _Nullable error) {
        if (onError) {
            onError(error);
        }
    }];
}

/// 投放&问卷-客户端-问卷详情
- (void)getSurveyDetailWithSurveyId:(NSString *)surveyId
                          onSuccess:(void (^)(NSDictionary *dict))onSuccess
                            onError:(void (^)(NSError *error))onError {
    NSMutableDictionary *parameter = [NSMutableDictionary dictionary];
    [parameter imy_setNonNilObject:surveyId forKey:@"id"];
    
    RACSignal *signal = [IMYServerRequest postPath:@"v4/survey/detail" host:my_survey_seeyouyima_com params:parameter headers:nil];
    
    @weakify(self);
    RACSignal *resSignal = [signal flattenMap:^__kindof RACStream * _Nullable(id  _Nullable value) {
        @strongify(self);
        return [self.class responseFlattenMap:value];
    }];
    
    [[resSignal deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
        if (onSuccess) {
            onSuccess(x);
        }
    } error:^(NSError * _Nullable error) {
        if (onError) {
            onError(error);
        }
    }];
}

/// IMYHTTPResponse 转 NSDictionary 或 error
+ (RACSignal *)responseFlattenMap:(IMYHTTPResponse *)x {
    if ([x.responseObject isKindOfClass:[NSDictionary class]]) {
        return [RACSignal return:x.responseObject];
    } else {
        NSMutableDictionary *userInfo = [NSMutableDictionary dictionaryWithDictionary:x.userInfo];
        userInfo[AFNetworkingOperationFailingURLResponseDataErrorKey] = x.responseData;
        userInfo[AFNetworkingOperationFailingURLResponseErrorKey] = x.response;
        userInfo[NSLocalizedDescriptionKey] = @"网络缓慢，请稍后再试";
        return [RACSignal error:[NSError errorWithDomain:@"ChatAI" code:-9 userInfo:userInfo]];
    }
}

/// 处理问卷数据，根据实际问卷结构创建本地问卷模型
- (IMYSurveyModel *)processSurveyModel:(IMYSurveyModel *)originalModel
                      withFeedbackWords:(NSArray<NSString *> *)feedbackWords
                                 scheme:(IMYNativeFeedbackScheme)scheme {
    // 创建新的问卷模型，保持原有基本信息
    IMYSurveyModel *processedModel = [[IMYSurveyModel alloc] init];
    processedModel.code = originalModel.code;
    processedModel.id = originalModel.id;
    processedModel.title = originalModel.title ?: @"猜你想搜反馈";
    processedModel.local_isNegative = YES;

    // 基于原问卷数据创建本地使用的问题列表
    NSMutableArray *questionList = [NSMutableArray array];
    
    if (originalModel.question_list.count >= 2) {
        // 第一个问题：将"哪些词不满意"转换为填空题，使用实际的question_id
        IMYSurveyQModel *originalWordsQuestion = originalModel.question_list[0];
        IMYSurveyQModel *wordsQuestion = [[IMYSurveyQModel alloc] init];
        wordsQuestion.question_id = originalWordsQuestion.question_id;
        wordsQuestion.text = originalWordsQuestion.text;
        wordsQuestion.type = IMYSurveyQuestionType_TX; // 填空题
        wordsQuestion.input_placeholder = @"请选择不满意的词条";
        
        // 创建选项供UI展示（但不用于提交，填空题通过local_TextViewText提交）
        NSMutableArray *wordOptions = [NSMutableArray array];
        for (NSInteger i = 0; i < feedbackWords.count; i++) {
            IMYSurveyOptionModel *option = [[IMYSurveyOptionModel alloc] init];
//            option.option_id = [NSString stringWithFormat:@"word_%ld", i];
            option.text = feedbackWords[i];
            option.local_isSelected = NO;
            [wordOptions addObject:option];
        }
        wordsQuestion.option_list = wordOptions;
        [questionList addObject:wordsQuestion];

        // 第二个问题：不满意原因，保持原有结构
        IMYSurveyQModel *reasonsQuestion = [[IMYSurveyQModel alloc] init];
        IMYSurveyQModel *originalReasonsQuestion = originalModel.question_list[1];
        reasonsQuestion.question_id = originalReasonsQuestion.question_id;
        reasonsQuestion.text = originalReasonsQuestion.text;
        reasonsQuestion.type = originalReasonsQuestion.type;
        reasonsQuestion.input_placeholder = originalReasonsQuestion.input_placeholder;
        reasonsQuestion.option_list = originalReasonsQuestion.option_list;
        [questionList addObject:reasonsQuestion];
        
        // 如果有第三个问题（如"其他"的输入框），也添加进来
        if (originalModel.question_list.count > 2) {
            IMYSurveyQModel *thirdQuestion = [[IMYSurveyQModel alloc] init];
            IMYSurveyQModel *originalThirdQuestion = originalModel.question_list[2];
            thirdQuestion.question_id = originalThirdQuestion.question_id;
            thirdQuestion.father_id = originalThirdQuestion.father_id;
            thirdQuestion.father_ids = originalThirdQuestion.father_ids;
            thirdQuestion.text = originalThirdQuestion.text;
            thirdQuestion.type = originalThirdQuestion.type;
            thirdQuestion.input_placeholder = originalThirdQuestion.input_placeholder;
            [questionList addObject:thirdQuestion];
        }
    }

    processedModel.question_list = questionList;
    processedModel.use_question_list = questionList;

    return processedModel;
}

/// 显示卡片
- (void)showCardWithSurveyModel:(IMYSurveyModel *)surveyModel {
    // 创建并显示卡片
    self.currentCardView = [[IMYNativeFeedbackCardView alloc] initWithFrame:[UIScreen mainScreen].bounds];

    @weakify(self);
    [self.currentCardView setOnSubmit:^(BOOL success, IMYSurveyModel *resultModel) {
        @strongify(self);
        if (success && resultModel) {
            [self submitSurveyModel:resultModel];
        } else {
            [self handleCardResult:NO surveyModel:resultModel];
        }
    }];
    // 关闭按钮回调，用于清空选中状态
    [self.currentCardView setOnClose:^{
        @strongify(self);
        // 清空预加载数据的选中状态
        if (self.surveyModel) {
            [self.surveyModel resetUseQuestionList];
        }
    }];
    [self.currentCardView showWithSurveyModel:surveyModel];
    
}

- (void)dismissCurrentCard {
    if (self.currentCardView) {
        [self.currentCardView dismiss];
        self.currentCardView = nil;
    }

    // 清空预加载数据的选中状态
    if (self.surveyModel) {
        [self.surveyModel resetUseQuestionList];
    }
}

- (void)handleCardResult:(BOOL)success surveyModel:(IMYSurveyModel *)surveyModel {
    self.currentCardView = nil;

    if (self.currentCompletion) {
        self.currentCompletion(success, surveyModel);
        self.currentCompletion = nil;
    }
}

#pragma mark - 提交逻辑

/// 提交问卷数据
- (void)submitSurveyModel:(IMYSurveyModel *)surveyModel {
    @weakify(self);
    [[IMYSurveyAPI sharedInstance] postSurveyAnswerWithSurveyId:surveyModel.id
                                                    surveyModel:surveyModel
                                                      onSuccess:^(NSDictionary *dict) {
        @strongify(self);
        [self handleSubmitSuccess:surveyModel];
    } onError:^(NSError *error) {
        @strongify(self);
        [self handleSubmitError:error];
    }];
}

/// 处理提交成功
- (void)handleSubmitSuccess:(IMYSurveyModel *)surveyModel {
    // 上报提交问卷结果埋点
    [self postSubmitEventTracking:surveyModel];

    // 清空预加载数据的选中状态
    if (self.surveyModel) {
        [self.surveyModel resetUseQuestionList];
    }

    [self dismissCurrentCard];
    // 显示成功提示
    [UIWindow imy_showTextHUD:@"感谢反馈，后续会持续优化"];
    [self handleCardResult:YES surveyModel:surveyModel];
}

/// 处理提交失败
- (void)handleSubmitError:(NSError *)error {
    // 显示错误提示
    if (error.code == 10001004) {
        [UIWindow imy_showTextHUD:@"还有内容未填写哦"];
    } else if (![IMYNetState networkEnable]) {
        [UIWindow imy_showTextHUD:@"网络不见了，请检查网络"];
    } else {
        [UIWindow imy_showTextHUD:@"提交失败，请重试"];
    }
}

#pragma mark - 埋点逻辑

/// 上报提交问卷结果埋点
- (void)postSubmitEventTracking:(IMYSurveyModel *)surveyModel {
    if (!surveyModel.use_question_list || surveyModel.use_question_list.count < 2) {
        return;
    }

    // 第一个问题：负反馈词条
    IMYSurveyQModel *wordsQuestion = surveyModel.use_question_list[0];
    NSMutableArray *selectedWords = [NSMutableArray array];
    for (IMYSurveyOptionModel *option in wordsQuestion.option_list) {
        if (option.local_isSelected) {
            [selectedWords addObject:option.text];
        }
    }

    // 第二个问题：不满意原因
    IMYSurveyQModel *reasonsQuestion = surveyModel.use_question_list[1];
    NSMutableArray *selectedReasons = [NSMutableArray array];

    for (IMYSurveyOptionModel *option in reasonsQuestion.option_list) {
        if (option.local_isSelected) {
            [selectedReasons addObject:option.text];
        }
    }

    // 获取"其他"选项的填写内容
    NSString *otherReasonText = @"";
    if (surveyModel.use_question_list.count > 2) {
        IMYSurveyQModel *thirdQuestion = surveyModel.use_question_list[2];
        otherReasonText = thirdQuestion.local_TextViewText ?: @"";
    }

    // 假设选择了负反馈词条a,b,c共m条，不满意原因为e,d,f共n条
    // 则上报m×n次，public_type只传负反馈词条，public_info传不满意原因
    for (NSString *word in selectedWords) {
        if (selectedReasons.count > 0) {
            for (NSString *reason in selectedReasons) {
                // public_type：只传负反馈词条
                NSString *publicType = word;

                // public_info：不满意原因处理
                NSString *publicInfo;
                if ([reason isEqualToString:@"其他"]) {
                    // 如果是"其他"选项，传填写的文本内容，没有内容则传空
                    publicInfo = otherReasonText;
                } else {
                    // 其他选项直接传原因文本
                    publicInfo = reason;
                }

                // 上报埋点：action固定为2（点击）
                [self postEventWitchAction:2
                               public_type:publicType
                               public_info:publicInfo];
            }
        } else {
            // 没有选择不满意原因，public_info传空
            [self postEventWitchAction:2
                           public_type:word
                           public_info:@""];
        }
    }
}

/// 提交问卷结果
/// - Parameters:
///   - action: 曝光点击, 固定值 2：点击
///   - public_type: 上报猜你想搜负反馈词条
///   - public_info: 上报负反馈原因中文
- (void)postEventWitchAction:(NSInteger)action
                     public_type:(NSString *)public_type
                     public_info:(NSString *)public_info {
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    [dict imy_setNonNilObject:@"sszjy_nrffk" forKey:@"event"];
    [dict imy_setNonNilObject:@(action) forKey:@"action"];
    [dict imy_setNonNilObject:public_type forKey:@"public_type"];
    // 如果public_info为空，传空字符串
    [dict setValue:public_info forKey:@"public_info"];

    [IMYGAEventHelper postWithPath:@"event" params:dict headers:nil completed:nil];
}


@end
