//
//  IMYFeedbackButtonCell.m
//  zzimymain
//
//

#import "IMYFeedbackButtonCell.h"
#import <IMYBaseKit/IMYBaseKit.h>

@implementation IMYFeedbackButtonCell

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.dynamicHeight = 94; // 默认高度
    }
    return self;
}

- (void)configureWithButton:(UIButton *)button {
    [self configureWithButton:button dynamicHeight:94];
}

- (void)configureWithButton:(UIButton *)button dynamicHeight:(CGFloat)height {
    self.dynamicHeight = MAX(height, 94); // 确保最小高度为94pt

    if (self.submitButton != button) {
        if (self.submitButton) {
            [self.submitButton removeFromSuperview];
        }

        self.submitButton = button;
        [self.contentView addSubview:button];
    }

    // 重新设置约束，让按钮固定在单元格底部
    [button mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(self.contentView).offset(12);
        make.right.mas_equalTo(self.contentView).offset(-12);
        make.height.mas_equalTo(48);
        make.bottom.mas_equalTo(self.contentView).offset(-34);
    }];
}

@end
