//
//  IMYFeedbackSeparatorCell.m
//  zzimymain
//
//

#import "IMYFeedbackSeparatorCell.h"
#import <IMYBaseKit/IMYBaseKit.h>

@implementation IMYFeedbackSeparatorCell

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.dynamicHeight = 12.5; // 默认最小高度
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.separatorLine = [[UIView alloc] init];
    [self.separatorLine imy_setBackgroundColorForKey:kCK_Black_E];
    [self.contentView addSubview:self.separatorLine];

    [self.separatorLine mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.contentView).offset(12);
        make.left.right.mas_equalTo(self.contentView);
        make.height.mas_equalTo(0.5);
    }];
}

- (void)configureWithDynamicHeight:(CGFloat)height {
    // 确保最小高度为12.5pt
    self.dynamicHeight = MAX(height, 12.5);
}

@end
