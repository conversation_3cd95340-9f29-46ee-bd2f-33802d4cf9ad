//
//  IMYNativeFeedbackCardView.m
//  zzimymain
//
//

#import "IMYNativeFeedbackCardView.h"
#import "IMYNativeFeedbackSelectionView.h"
#import "IMYFeedbackHeaderCell.h"
#import "IMYFeedbackContentCell.h"
#import "IMYFeedbackSeparatorCell.h"
#import "IMYFeedbackButtonCell.h"
#import <IMYBaseKit/IMYBaseKit.h>

static NSString * const kHeaderCellIdentifier = @"HeaderCell";
static NSString * const kContentCellIdentifier = @"ContentCell";
static NSString * const kSeparatorCellIdentifier = @"SeparatorCell";
static NSString * const kButtonCellIdentifier = @"ButtonCell";

@interface IMYNativeFeedbackCardView () <UICollectionViewDataSource, UICollectionViewDelegateFlowLayout>

@property (nonatomic, strong) IMYSurveyModel *surveyModel;
@property (nonatomic, strong) UIView *backgroundView;
@property (nonatomic, strong) UIView *cardView;
@property (nonatomic, strong) UICollectionView *collectionView;

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIButton *closeButton;

@property (nonatomic, strong) UILabel *wordsLabel;
@property (nonatomic, strong) IMYNativeFeedbackSelectionView *wordsSelectionView;

@property (nonatomic, strong) UILabel *reasonsLabel;
@property (nonatomic, strong) IMYNativeFeedbackSelectionView *reasonsSelectionView;

@property (nonatomic, strong) UIView *separatorLine;
@property (nonatomic, strong) UIButton *submitButton;

@property (nonatomic, assign) BOOL isKeyboardShowing;
@property (nonatomic, assign) CGFloat currentKeyboardHeight;
@property (nonatomic, strong) UIPanGestureRecognizer *keyboardDismissGesture;

@property (nonatomic, assign) BOOL isInitializing;

@property (nonatomic, assign) CGPoint savedContentOffset;
@property (nonatomic, assign) BOOL isEditing;

@property (nonatomic, assign) BOOL isUpdatingLayout;
@property (nonatomic, assign) BOOL shouldPreserveScrollPosition;

// 用于解决高度约束与滚动位置同步问题
@property (nonatomic, assign) CGFloat cardViewHeightBeforeUpdate;
@property (nonatomic, assign) CGFloat savedContentOffsetBeforeUpdate;


@end

@implementation IMYNativeFeedbackCardView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
        [self setupKeyboardNotifications];
    }
    return self;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)setupUI {
    [self addSubview:self.backgroundView];
    [self addSubview:self.cardView];
    
    [self.cardView addSubview:self.titleLabel];
    [self.cardView addSubview:self.closeButton];
    [self.cardView addSubview:self.collectionView];
    
    [self setupConstraints];
    [self setupGestures];
}

- (void)setupConstraints {
    [self.backgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(self);
    }];
    
    [self.cardView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.mas_equalTo(self);
        make.height.mas_equalTo([UIScreen mainScreen].bounds.size.height - 177);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.cardView).offset(0);
        make.centerX.mas_equalTo(self.cardView);
        make.height.mas_equalTo(49);
    }];
    
    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.cardView).offset(15);
        make.right.mas_equalTo(self.cardView).offset(-14);
        make.size.mas_equalTo(CGSizeMake(32, 32));
    }];
    
    [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.titleLabel.mas_bottom);
        make.left.right.mas_equalTo(self.cardView);
        make.bottom.mas_equalTo(self.cardView);
    }];
}

- (void)setupGestures {
    UITapGestureRecognizer *backgroundTap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(backgroundTapped)];
    [self.backgroundView addGestureRecognizer:backgroundTap];
}

- (void)setupKeyboardNotifications {
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillShow:)
                                                 name:UIKeyboardWillShowNotification
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardDidChange:)
                                                 name:UITextInputCurrentInputModeDidChangeNotification
                                               object:nil];

    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillHide:)
                                                 name:UIKeyboardWillHideNotification
                                               object:nil];

}

- (void)showWithSurveyModel:(IMYSurveyModel *)surveyModel {
    // 避免初始化时的动画冲突
    self.isInitializing = YES;

    self.surveyModel = surveyModel;

    self.titleLabel.text = surveyModel.title ?: @"猜你想搜反馈";

    NSArray<IMYSurveyQModel *> *questionList = surveyModel.question_list ?: surveyModel.use_question_list;

    if (questionList.count >= 2) {
        IMYSurveyQModel *wordsQuestion = questionList[0];
        self.wordsLabel.text = wordsQuestion.text ?: @"哪些词不满意？";

        IMYSurveyQModel *reasonsQuestion = questionList[1];
        self.reasonsLabel.text = reasonsQuestion.text ?: @"不满意的原因？";

        if (surveyModel.use_question_list.count >= 2) {
            [self.wordsSelectionView setupOptions:surveyModel.use_question_list[0].option_list withType:IMYSelectionViewTypeNormal];
            [self.reasonsSelectionView setupOptions:surveyModel.use_question_list[1].option_list withType:IMYSelectionViewTypeWithOther];
        }
    } else {
        self.wordsLabel.text = @"哪些词不满意？";
        self.reasonsLabel.text = @"不满意的原因？";
    }

    [self updateSubmitButtonState];

    UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
    if (!keyWindow) {
        keyWindow = [UIApplication sharedApplication].windows.firstObject;
    }
    [keyWindow addSubview:self];

    [self.collectionView reloadData];
    [self updateCardViewHeight];
    self.isInitializing = NO;

    self.backgroundView.alpha = 0;
    self.cardView.transform = CGAffineTransformMakeTranslation(0, self.cardView.frame.size.height);

    [UIView animateWithDuration:0.3 delay:0 options:UIViewAnimationOptionCurveEaseOut animations:^{
        self.backgroundView.alpha = 1;
        self.cardView.transform = CGAffineTransformIdentity;
    } completion:nil];
}

- (void)dismiss {
    self.isInitializing = NO;

    [UIView animateWithDuration:0.3 delay:0 options:UIViewAnimationOptionCurveEaseIn animations:^{
        self.backgroundView.alpha = 0;
        self.cardView.transform = CGAffineTransformMakeTranslation(0, self.cardView.frame.size.height);
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
}

- (void)updateSubmitButtonState {
    BOOL hasSelectedWords = [self hasSelectedWords];

    if (hasSelectedWords) {
        self.submitButton.enabled = YES;
        [self.submitButton imy_setTitleColor:@"FFFFFF"];
        [self.submitButton imy_setBackgroundColor:kCK_Red_A];
    } else {
        self.submitButton.enabled = NO;
        [self.submitButton imy_setTitleColor:@"#B7B7B7"];
        [self.submitButton imy_setBackgroundColor:[[UIColor imy_colorForKey:@"#B7B7B7"] colorWithAlphaComponent:0.3]];
    }
}

- (BOOL)hasSelectedWords {
    if (self.surveyModel.use_question_list.count >= 1) {
        IMYSurveyQModel *wordsQuestion = self.surveyModel.use_question_list[0];
        for (IMYSurveyOptionModel *option in wordsQuestion.option_list) {
            if (option.local_isSelected) {
                return YES;
            }
        }
    }
    return NO;
}

- (BOOL)isOtherReasonSelected {
    if (self.surveyModel.use_question_list.count >= 2) {
        IMYSurveyQModel *reasonsQuestion = self.surveyModel.use_question_list[1];
        for (IMYSurveyOptionModel *option in reasonsQuestion.option_list) {
            if ([option.text isEqualToString:@"其他"] && option.local_isSelected) {
                return YES;
            }
        }
    }
    return NO;
}

- (void)backgroundTapped {
    [self closeButtonTapped];
}

- (void)dismissKeyboard {
    [self endEditing:YES];
}

- (void)closeButtonTapped {
    [self endEditing:YES];
    [self dismiss];
    if (self.onClose) {
        self.onClose();
    }
}

- (void)submitButtonTapped {
    if (![self hasSelectedWords]) {
        return;
    }

    [self prepareSubmitData];
    [self endEditing:YES];
    if (self.onSubmit) {
        self.onSubmit(YES, self.surveyModel);
    }
}

- (void)prepareSubmitData {
    if (self.surveyModel.use_question_list.count < 2) {
        return;
    }

    // 将选中的词条拼接为文本答案
    IMYSurveyQModel *wordsQuestion = self.surveyModel.use_question_list[0];
    NSMutableArray *selectedWords = [NSMutableArray array];
    for (IMYSurveyOptionModel *option in wordsQuestion.option_list) {
        if (option.local_isSelected) {
            [selectedWords addObject:option.text];
        }
    }
    wordsQuestion.local_TextViewText = [selectedWords componentsJoinedByString:@","];

    // 保存"其他"选项的文本内容
    if (self.surveyModel.use_question_list.count > 2 && [self isOtherReasonSelected]) {
        NSString *otherText = [self.reasonsSelectionView getOtherReasonText];
        IMYSurveyQModel *thirdQuestion = self.surveyModel.use_question_list[2];
        thirdQuestion.local_TextViewText = otherText;
    }
}

- (void)selectionChanged {
    [self updateSubmitButtonState];

    if (self.isInitializing) {
        [self updateCardViewHeight];
    } else {
        [self updateLayoutWithScrollPositionPreservation:YES];
    }
}

- (void)updateCardViewHeight {
    // 保持固定高度设置不变，分割线的弹性高度通过CollectionView的布局来实现
    CGFloat maxHeight = [UIScreen mainScreen].bounds.size.height - 177;

    [self.cardView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.mas_equalTo(self);
        make.height.mas_equalTo(maxHeight);
    }];
    [self layoutIfNeeded];
}

- (void)keyboardWillShow:(NSNotification *)notification {
    if (self.isKeyboardShowing) return;

    NSDictionary *userInfo = notification.userInfo;
    CGRect keyboardFrame = [userInfo[UIKeyboardFrameEndUserInfoKey] CGRectValue];
    CGFloat keyboardHeight = keyboardFrame.size.height;
    NSTimeInterval animationDuration = [userInfo[UIKeyboardAnimationDurationUserInfoKey] doubleValue];

    self.isKeyboardShowing = YES;
    self.currentKeyboardHeight = keyboardHeight;

    if (!self.keyboardDismissGesture.view) {
        [self.cardView addGestureRecognizer:self.keyboardDismissGesture];
    }

    // 刷新布局，切换到固定高度模式
    [UIView animateWithDuration:animationDuration animations:^{
        [self.collectionView.collectionViewLayout invalidateLayout];
        [self.collectionView layoutIfNeeded];

        // 智能调整滚动位置
        [self adjustScrollPositionForKeyboard:keyboardHeight];
    }];

    self.collectionView.scrollEnabled = NO;
}

- (void)adjustScrollPositionForKeyboard:(CGFloat)keyboardHeight {
    UIWindow *window = [UIApplication sharedApplication].delegate.window;
    if (!window) {
        window = [UIApplication sharedApplication].windows.firstObject;
    }

    CGFloat keyboardTop = [UIScreen mainScreen].bounds.size.height - keyboardHeight;
    CGFloat targetVisibleBottom = keyboardTop - 20; // 键盘上方20pt的安全距离

    // 优先确保正在编辑的文本输入框可见
    UITextView *editingTextView = [self getCurrentEditingTextView];
    if (editingTextView) {
        CGRect textViewFrame = [editingTextView convertRect:editingTextView.bounds toView:window];
        CGFloat textViewBottom = textViewFrame.origin.y + textViewFrame.size.height;

        if (textViewBottom > targetVisibleBottom) {
            CGFloat scrollOffset = textViewBottom - targetVisibleBottom;
            CGFloat currentOffset = self.collectionView.contentOffset.y;
            self.collectionView.contentOffset = CGPointMake(0, currentOffset + scrollOffset);
            return;
        }
    }

    // 如果没有正在编辑的文本框，确保提交按钮可见
    CGFloat submitButtonBottom = [self.submitButton convertRect:self.submitButton.bounds toView:window].origin.y + self.submitButton.frame.size.height;
    if (submitButtonBottom > targetVisibleBottom) {
        CGFloat scrollOffset = submitButtonBottom - targetVisibleBottom;
        CGFloat currentOffset = self.collectionView.contentOffset.y;
        self.collectionView.contentOffset = CGPointMake(0, currentOffset + scrollOffset);
    }
}

- (void)keyboardDidChange:(NSNotification *)notification {
    self.isKeyboardShowing = NO;
}

- (void)keyboardWillHide:(NSNotification *)notification {
    if (!self.isKeyboardShowing) return;

    NSDictionary *userInfo = notification.userInfo;
    NSTimeInterval animationDuration = [userInfo[UIKeyboardAnimationDurationUserInfoKey] doubleValue];

    self.isKeyboardShowing = NO;
    self.currentKeyboardHeight = 0;

    if (self.keyboardDismissGesture.view) {
        [self.cardView removeGestureRecognizer:self.keyboardDismissGesture];
    }

    // 恢复弹性布局并调整滚动位置
    [UIView animateWithDuration:animationDuration animations:^{
        [self.collectionView.collectionViewLayout invalidateLayout];
        [self.collectionView layoutIfNeeded];

        // 智能调整滚动位置
        [self adjustScrollPositionAfterKeyboardHide];
    }];

    self.collectionView.scrollEnabled = YES;
}

- (void)adjustScrollPositionAfterKeyboardHide {
    // 键盘收起后，优先恢复到合理的滚动位置
    CGFloat currentOffset = self.collectionView.contentOffset.y;

    // 如果当前有选中"其他"选项且有文本内容，保持当前位置
    if ([self isOtherReasonSelected] && !CGPointEqualToPoint(self.savedContentOffset, CGPointZero)) {
        CGFloat maxOffsetY = self.collectionView.contentSize.height - self.collectionView.frame.size.height;
        if (maxOffsetY < 0) maxOffsetY = 0;

        CGFloat targetOffset = MIN(self.savedContentOffset.y, maxOffsetY);
        self.collectionView.contentOffset = CGPointMake(0, MAX(targetOffset, 0));
        return;
    }

    // 否则，尝试滚动到顶部以展示最佳布局效果
    CGFloat maxOffsetY = self.collectionView.contentSize.height - self.collectionView.frame.size.height;
    if (maxOffsetY < 0) {
        maxOffsetY = 0;
    }

    if (currentOffset > maxOffsetY) {
        self.collectionView.contentOffset = CGPointMake(0, maxOffsetY);
    } else if (currentOffset < 0) {
        self.collectionView.contentOffset = CGPointZero;
    }
    // 如果当前位置合理，保持不变
}

- (void)handleOtherTextViewHeightChanged:(CGFloat)heightDifference {
    if (fabs(heightDifference) < 0.1) {
        return;
    }

    self.isEditing = YES;

    // 保存状态用于计算实际高度变化
    self.cardViewHeightBeforeUpdate = self.cardView.frame.size.height;
    self.savedContentOffsetBeforeUpdate = self.collectionView.contentOffset.y;

    [self updateLayoutWithScrollPositionPreservation:NO];

    dispatch_async(dispatch_get_main_queue(), ^{
        [self adjustContentOffsetBasedOnActualHeightChange:heightDifference];
        self.isEditing = NO;
    });
}

- (void)adjustContentOffsetBasedOnActualHeightChange:(CGFloat)heightDifference {
    CGFloat currentCardViewHeight = self.cardView.frame.size.height;
    CGFloat actualHeightChange = currentCardViewHeight - self.cardViewHeightBeforeUpdate;

    CGFloat newOffsetY = self.savedContentOffsetBeforeUpdate + heightDifference - actualHeightChange;

    CGPoint targetOffset = CGPointMake(0, newOffsetY);
    [self.collectionView setContentOffset:targetOffset animated:NO];

    self.savedContentOffset = targetOffset;
}
- (void)updateLayoutWithScrollPositionPreservation:(BOOL)preservePosition {
    if (self.isUpdatingLayout) {
        return;
    }

    self.isUpdatingLayout = YES;
    self.shouldPreserveScrollPosition = preservePosition;

    if (preservePosition && !self.isEditing && [self isOtherReasonSelected]) {
        self.savedContentOffset = self.collectionView.contentOffset;
    }

    [self.collectionView.collectionViewLayout invalidateLayout];
    [self updateCardViewHeight];

    dispatch_async(dispatch_get_main_queue(), ^{
        [self restoreScrollPositionIfNeeded];
        self.isUpdatingLayout = NO;
    });
}

- (void)restoreScrollPositionIfNeeded {
    if (!self.shouldPreserveScrollPosition) {
        return;
    }

    if ([self isOtherReasonSelected]) {
        if (!CGPointEqualToPoint(self.savedContentOffset, CGPointZero)) {
            CGPoint targetOffset = CGPointMake(0, self.savedContentOffset.y);
            [self.collectionView setContentOffset:targetOffset animated:NO];
        }
    } else {
        [self.collectionView setContentOffset:CGPointZero animated:NO];
        self.savedContentOffset = CGPointZero;
    }
}

- (CGFloat)calculateButtonCellDynamicHeight {
    // 计算已使用的高度（不包括按钮单元格）
    CGFloat titleHeight = 49;
    CGFloat wordsHeaderHeight = 44;
    CGFloat wordsContentHeight = self.wordsSelectionView.calculatedHeight;
    CGFloat reasonsHeaderHeight = 52;
    CGFloat reasonsContentHeight = self.reasonsSelectionView.calculatedHeight;
    CGFloat separatorHeight = 12.5; // 分割线固定高度

    CGFloat usedHeight = titleHeight + wordsHeaderHeight + wordsContentHeight +
                        reasonsHeaderHeight + reasonsContentHeight + separatorHeight;

    // 计算可用总高度
    CGFloat totalAvailableHeight = [UIScreen mainScreen].bounds.size.height - 177;

    // 计算剩余空间分配给按钮单元格，确保最小高度为94pt
    CGFloat remainingHeight = totalAvailableHeight - usedHeight;
    return MAX(remainingHeight, 94);
}

- (UITextView *)getCurrentEditingTextView {
    // 检测当前正在编辑的文本输入框
    if (self.reasonsSelectionView.otherTextView && self.reasonsSelectionView.otherTextView.isFirstResponder) {
        return self.reasonsSelectionView.otherTextView;
    }
    return nil;
}

- (UIPanGestureRecognizer *)keyboardDismissGesture {
    if (!_keyboardDismissGesture) {
        _keyboardDismissGesture = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(dismissKeyboard)];
        _keyboardDismissGesture.delegate = self;
    }
    return _keyboardDismissGesture;
}

- (UIView *)backgroundView {
    if (!_backgroundView) {
        _backgroundView = [[UIView alloc] init];
        [_backgroundView imy_setBackgroundColor:[[UIColor imy_colorForKey:kCK_Black_A] colorWithAlphaComponent:0.4]];
    }
    return _backgroundView;
}

- (UIView *)cardView {
    if (!_cardView) {
        _cardView = [[UIView alloc] init];
        [_cardView imy_setBackgroundColorForKey:kCK_White_AT];
        _cardView.layer.cornerRadius = 16;
        _cardView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
        _cardView.layer.masksToBounds = YES;
    }
    return _cardView;
}

- (UICollectionView *)collectionView {
    if (!_collectionView) {
        UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
        layout.scrollDirection = UICollectionViewScrollDirectionVertical;
        layout.minimumLineSpacing = 0;
        layout.minimumInteritemSpacing = 0;
        
        _collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:layout];
        _collectionView.backgroundColor = [UIColor clearColor];
        _collectionView.showsVerticalScrollIndicator = NO;
        _collectionView.showsHorizontalScrollIndicator = NO;
        _collectionView.dataSource = self;
        _collectionView.delegate = self;
        
        // 注册 cell 类型
        [_collectionView registerClass:[IMYFeedbackHeaderCell class] forCellWithReuseIdentifier:kHeaderCellIdentifier];
        [_collectionView registerClass:[IMYFeedbackContentCell class] forCellWithReuseIdentifier:kContentCellIdentifier];
        [_collectionView registerClass:[IMYFeedbackSeparatorCell class] forCellWithReuseIdentifier:kSeparatorCellIdentifier];
        [_collectionView registerClass:[IMYFeedbackButtonCell class] forCellWithReuseIdentifier:kButtonCellIdentifier];
    }
    return _collectionView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.text = @"猜你想搜反馈";
        _titleLabel.font = [UIFont boldSystemFontOfSize:18];
        [_titleLabel imy_setTextColor:kCK_Black_A];
        _titleLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _titleLabel;
}

- (UIButton *)closeButton {
    if (!_closeButton) {
        _closeButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_closeButton imy_setImage:[UIImage imy_imageForKey:@"search_pop_close_btn"]];
        [_closeButton addTarget:self action:@selector(closeButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    }
    return _closeButton;
}

- (UILabel *)wordsLabel {
    if (!_wordsLabel) {
        _wordsLabel = [[UILabel alloc] init];
        _wordsLabel.text = @"哪些词不满意?";
        _wordsLabel.font = [UIFont systemFontOfSize:14];
        [_wordsLabel imy_setTextColor:kCK_Black_M];
    }
    return _wordsLabel;
}

- (IMYNativeFeedbackSelectionView *)wordsSelectionView {
    if (!_wordsSelectionView) {
        _wordsSelectionView = [[IMYNativeFeedbackSelectionView alloc] init];
        @weakify(self);
        _wordsSelectionView.onSelectionChanged = ^{
            @strongify(self);
            [self selectionChanged];
        };
    }
    return _wordsSelectionView;
}

- (UILabel *)reasonsLabel {
    if (!_reasonsLabel) {
        _reasonsLabel = [[UILabel alloc] init];
        _reasonsLabel.text = @"不满意的原因?";
        _reasonsLabel.font = [UIFont systemFontOfSize:14];
        [_reasonsLabel imy_setTextColor:kCK_Black_M];
    }
    return _reasonsLabel;
}

- (IMYNativeFeedbackSelectionView *)reasonsSelectionView {
    if (!_reasonsSelectionView) {
        _reasonsSelectionView = [[IMYNativeFeedbackSelectionView alloc] init];
        @weakify(self);
        _reasonsSelectionView.onSelectionChanged = ^{
            @strongify(self);
            [self selectionChanged];
        };
        _reasonsSelectionView.onTextViewHeightChanged = ^(CGFloat heightDifference) {
            @strongify(self);
            [self handleOtherTextViewHeightChanged:heightDifference];
        };
    }
    return _reasonsSelectionView;
}

- (UIView *)separatorLine {
    if (!_separatorLine) {
        _separatorLine = [[UIView alloc] init];
        [_separatorLine imy_setBackgroundColorForKey:kCK_Black_E];
    }
    return _separatorLine;
}

- (UIButton *)submitButton {
    if (!_submitButton) {
        _submitButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_submitButton setTitle:@"提交" forState:UIControlStateNormal];
        _submitButton.titleLabel.font = [UIFont systemFontOfSize:16];
        _submitButton.layer.cornerRadius = 24;
        _submitButton.layer.masksToBounds = YES;
        [_submitButton addTarget:self action:@selector(submitButtonTapped) forControlEvents:UIControlEventTouchUpInside];

        _submitButton.enabled = NO;
        [_submitButton imy_setTitleColor:kCK_Red_A];
        [_submitButton imy_setBackgroundColor:@"#FFDBE7"];
    }
    return _submitButton;
}

- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView {
    return 2;
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    if (section == 0) {
        return 2;
    } else if (section == 1) {
        return 4;
    }
    return 0;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section == 0) {
        if (indexPath.item == 0) {
            IMYFeedbackHeaderCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:kHeaderCellIdentifier forIndexPath:indexPath];
            [cell configureWithTitle:self.wordsLabel.text];
            return cell;
        } else {
            IMYFeedbackContentCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:kContentCellIdentifier forIndexPath:indexPath];
            [cell configureWithSelectionView:self.wordsSelectionView];
            return cell;
        }
    } else {
        if (indexPath.item == 0) {
            IMYFeedbackHeaderCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:kHeaderCellIdentifier forIndexPath:indexPath];
            [cell configureWithTitle:self.reasonsLabel.text];
            return cell;
        } else if (indexPath.item == 1) {
            IMYFeedbackContentCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:kContentCellIdentifier forIndexPath:indexPath];
            [cell configureWithSelectionView:self.reasonsSelectionView];
            return cell;
        } else if (indexPath.item == 2) {
            IMYFeedbackSeparatorCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:kSeparatorCellIdentifier forIndexPath:indexPath];
            return cell;
        } else {
            IMYFeedbackButtonCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:kButtonCellIdentifier forIndexPath:indexPath];
            CGFloat dynamicHeight = [self calculateButtonCellDynamicHeight];
            [cell configureWithButton:self.submitButton dynamicHeight:dynamicHeight];
            return cell;
        }
    }
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    CGFloat width = collectionView.frame.size.width;

    if (indexPath.section == 0) {
        if (indexPath.item == 0) {
            return CGSizeMake(width, 44);
        } else {
            return CGSizeMake(width, self.wordsSelectionView.calculatedHeight);
        }
    } else {
        if (indexPath.item == 0) {
            return CGSizeMake(width, 52);
        } else if (indexPath.item == 1) {
            return CGSizeMake(width, self.reasonsSelectionView.calculatedHeight);
        } else if (indexPath.item == 2) {
            return CGSizeMake(width, 12.5);
        } else {
            // 按钮单元格根据键盘状态使用不同高度策略
            if (self.isKeyboardShowing) {
                // 键盘显示时使用固定最小高度，确保布局稳定
                return CGSizeMake(width, 94);
            } else {
                // 键盘隐藏时使用弹性高度，实现最佳布局
                CGFloat dynamicHeight = [self calculateButtonCellDynamicHeight];
                return CGSizeMake(width, dynamicHeight);
            }
        }
    }
}

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldRecognizeSimultaneouslyWithGestureRecognizer:(UIGestureRecognizer *)otherGestureRecognizer {
    return YES;
}

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch {
    if (gestureRecognizer == self.keyboardDismissGesture) {
        if ([self isTouchInOtherTextView:touch]) {
            return NO;
        }
        return YES;
    }

    return YES;
}

- (BOOL)isTouchInOtherTextView:(UITouch *)touch {
    CGPoint touchPoint = [touch locationInView:self];
    NSArray *selectionViews = @[self.wordsSelectionView, self.reasonsSelectionView];

    for (IMYNativeFeedbackSelectionView *selectionView in selectionViews) {
        if (selectionView && selectionView.otherTextView && selectionView.otherTextView.superview) {
            CGPoint pointInTextView = [self convertPoint:touchPoint toView:selectionView.otherTextView];
            if (CGRectContainsPoint(selectionView.otherTextView.bounds, pointInTextView)) {
                return YES;
            }
        }
    }

    return NO;
}

@end
